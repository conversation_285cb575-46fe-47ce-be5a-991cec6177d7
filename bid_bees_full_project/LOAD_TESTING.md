# Performance and Load Testing Plan

This document outlines the strategy for performance and load testing the BidBees platform. The goal is to ensure the system is responsive, stable, and scalable under production-level traffic.

## 1. Tooling

We will use **k6** ([https://k6.io](https://k6.io)), an open-source load testing tool, for its developer-friendly ergonomics, performance, and excellent reporting capabilities.

### Installation

To run the tests locally, install k6 by following the official instructions: [Installing k6](https://k6.io/docs/getting-started/installation/)

## 2. Testing Strategy

We will employ several types of tests to evaluate system performance:

- **Smoke Test:** A minimal test run with 1 virtual user to verify that the system is up and the test script is working correctly.
- **Load Test:** Simulates expected production traffic to measure performance against our defined goals.
- **Stress Test:** Pushes the system beyond its limits to identify its breaking point and understand its failure modes.
- **Soak Test:** A long-duration test to check for memory leaks or performance degradation over time.

## 3. Performance Goals (SLOs)

- **API Response Time:** The 95th percentile (p95) response time for all critical API endpoints should be **under 500ms**.
- **Error Rate:** The test should have an error rate of **less than 0.1%**.
- **Throughput:** The system should handle at least **200 requests per second (RPS)**.

## 4. How to Run Load Tests

Load tests are defined in the `tests/` directory and can be run using a simple npm script.

### Running the Test

```bash
# Make sure all services are running first
./validate-and-start.sh

# Run the load test
npm run test:load
```

This command will execute the k6 script located at `tests/load-test.js`.

### Example Test Output

The results will be displayed in the console, providing key metrics:

```
     ✓ API is healthy

     checks.........................: 100.00% ✓ 1        ✗ 0
     data_received..................: 13 B    0 B/s
     data_sent......................: 84 B    0 B/s
     http_req_blocked...............: avg=95.2ms   min=95.2ms   med=95.2ms   max=95.2ms   p(90)=95.2ms   p(95)=95.2ms
     http_req_connecting............: avg=32.1ms   min=32.1ms   med=32.1ms   max=32.1ms   p(90)=32.1ms   p(95)=32.1ms
     http_req_duration..............: avg=157.3ms  min=157.3ms  med=157.3ms  max=157.3ms  p(90)=157.3ms  p(95)=157.3ms
       { expected_response:true }...: avg=157.3ms  min=157.3ms  med=157.3ms  max=157.3ms  p(90)=157.3ms  p(95)=157.3ms
     http_req_failed................: 0.00%   ✓ 0        ✗ 1
     http_req_receiving.............: avg=0s       min=0s       med=0s       max=0s       p(90)=0s       p(95)=0s
     http_req_sending...............: avg=0s       min=0s       med=0s       max=0s       p(90)=0s       p(95)=0s
     http_req_tls_handshaking.......: avg=62.9ms   min=62.9ms   med=62.9ms   max=62.9ms   p(90)=62.9ms   p(95)=62.9ms
     http_req_waiting...............: avg=157.3ms  min=157.3ms  med=157.3ms  max=157.3ms  p(90)=157.3ms  p(95)=157.3ms
     http_reqs......................: 1       0.48/s
     iteration_duration.............: avg=2.06s    min=2.06s    med=2.06s    max=2.06s    p(90)=2.06s    p(95)=2.06s
     iterations.....................: 1       0.48/s
     vus............................: 1       min=1      max=1
     vus_max........................: 1       min=1      max=1
```

## 5. CI/CD Integration

Load tests will be integrated into the CI/CD pipeline to run automatically in the staging environment. This will allow us to catch performance regressions before they reach production.
