import http from 'k6/http';
import { check, sleep } from 'k6';

// The base URL for the API Gateway
const API_BASE_URL = 'http://localhost:8080';

export const options = {
  // Simulate 10 virtual users
  vus: 10,
  // For a duration of 30 seconds
  duration: '30s',
  // Define thresholds for success
  thresholds: {
    'http_req_duration': ['p(95)<500'], // 95% of requests must complete below 500ms
    'http_req_failed': ['rate<0.01'],   // Error rate must be less than 1%
  },
};

export default function () {
  // Test the health check endpoint
  const res = http.get(`${API_BASE_URL}/health`);

  // Check if the response is successful
  check(res, {
    'API is healthy': (r) => r.status === 200,
  });

  // Wait for 1 second before the next iteration
  sleep(1);
}
