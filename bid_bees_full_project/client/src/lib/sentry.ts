import * as Sentry from '@sentry/react';
import { Replay } from '@sentry/react/replay';


interface SentryConfig {
  dsn: string;
  environment: string;
  release?: string;
  tracesSampleRate: number;
  replaysSessionSampleRate: number;
  replaysOnErrorSampleRate: number;
  beforeSend?: (event: Sentry.Event) => Sentry.Event | null;
}

export class SentryService {
  private static instance: SentryService;
  private initialized = false;

  private constructor() {}

  public static getInstance(): SentryService {
    if (!SentryService.instance) {
      SentryService.instance = new SentryService();
    }
    return SentryService.instance;
  }

  public initialize(config: Partial<SentryConfig> = {}): void {
    if (this.initialized) {
      console.warn('Sentry already initialized');
      return;
    }

    const dsn = config.dsn || import.meta.env.VITE_SENTRY_DSN;
    if (!dsn) {
      console.warn('Sentry DSN not provided, skipping initialization');
      return;
    }

    const environment = config.environment || import.meta.env.VITE_NODE_ENV || 'development';
    const release = config.release || import.meta.env.VITE_APP_VERSION || '1.0.0';

    Sentry.init({
      dsn,
      environment,
      release: `bid-bees-client@${release}`,
      tracesSampleRate: config.tracesSampleRate || (environment === 'production' ? 0.1 : 1.0),
      
      // Session Replay
      replaysSessionSampleRate: config.replaysSessionSampleRate || (environment === 'production' ? 0.01 : 0.1),
      replaysOnErrorSampleRate: config.replaysOnErrorSampleRate || 1.0,

      integrations: [
        Sentry.reactRouterV6BrowserTracingIntegration({
          useEffect: React.useEffect,
          useLocation,
          useNavigationType,
          createRoutesFromChildren,
          matchRoutes,
        }),
        new Sentry.Replay({
          // Additional configuration for Session Replay
          maskAllText: true,
          blockAllMedia: true,
        }),
      ],

      beforeSend: config.beforeSend || this.defaultBeforeSend,

      // Performance monitoring
      beforeSendTransaction(event) {
        // Filter out health check and static asset transactions
        if (event.transaction?.includes('/health') || 
            event.transaction?.includes('/assets/') ||
            event.transaction?.includes('/_vite/')) {
          return null;
        }
        return event;
      },

      // Error filtering
      ignoreErrors: [
        // Browser extensions
        'Non-Error promise rejection captured',
        'ResizeObserver loop limit exceeded',
        'Script error.',
        // Network errors
        'NetworkError',
        'fetch',
        'ChunkLoadError',
        'Loading chunk',
        // React DevTools
        'ReactDevTools',
        // Common user errors
        'AbortError',
        'The operation was aborted',
      ],

      // Tags for better organization
      initialScope: {
        tags: {
          component: 'bid-bees-client',
          service: 'react-app',
        },
      },
    });

    this.initialized = true;
    console.log(`Sentry initialized for environment: ${environment}`);
  }

  private defaultBeforeSend(event: Sentry.Event): Sentry.Event | null {
    // Don't send events in development unless explicitly enabled
    if (import.meta.env.DEV && !import.meta.env.VITE_SENTRY_ENABLE_DEV) {
      return null;
    }

    // Filter out sensitive information
    if (event.request?.data) {
      const sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
      const data = event.request.data;
      
      if (typeof data === 'object') {
        sensitiveFields.forEach(field => {
          if (data[field]) {
            data[field] = '[Filtered]';
          }
        });
      }
    }

    return event;
  }

  public captureException(error: Error, context?: Record<string, any>): string {
    return Sentry.captureException(error, {
      tags: context?.tags,
      extra: context?.extra,
      user: context?.user,
      level: context?.level || 'error',
    });
  }

  public captureMessage(message: string, level: Sentry.SeverityLevel = 'info', context?: Record<string, any>): string {
    return Sentry.captureMessage(message, {
      level,
      tags: context?.tags,
      extra: context?.extra,
      user: context?.user,
    });
  }

  public addBreadcrumb(breadcrumb: Sentry.Breadcrumb): void {
    Sentry.addBreadcrumb(breadcrumb);
  }

  public setUser(user: { id?: string; email?: string; username?: string }): void {
    Sentry.setUser(user);
  }

  public setTag(key: string, value: string): void {
    Sentry.setTag(key, value);
  }

  public setContext(key: string, context: Record<string, any>): void {
    Sentry.setContext(key, context);
  }

  

  // React-specific helpers
  public captureUserAction(action: string, component: string, data?: Record<string, any>): void {
    this.addBreadcrumb({
      message: `User Action: ${action}`,
      category: 'user',
      level: 'info',
      data: {
        component,
        ...data,
      },
    });
  }

  public captureNavigationEvent(from: string, to: string): void {
    this.addBreadcrumb({
      message: `Navigation: ${from} -> ${to}`,
      category: 'navigation',
      level: 'info',
      data: { from, to },
    });
  }

  public captureAPICall(url: string, method: string, status: number, duration: number): void {
    this.addBreadcrumb({
      message: `API Call: ${method} ${url}`,
      category: 'http',
      level: status >= 400 ? 'error' : 'info',
      data: {
        url,
        method,
        status,
        duration,
      },
    });

    if (status >= 400) {
      this.captureMessage(`API Error: ${method} ${url}`, 'error', {
        tags: { type: 'api_error' },
        extra: { url, method, status, duration },
      });
    }
  }

  public captureFormError(formName: string, field: string, error: string): void {
    this.addBreadcrumb({
      message: `Form Error: ${formName}.${field}`,
      category: 'form',
      level: 'error',
      data: {
        formName,
        field,
        error,
      },
    });

    this.captureMessage(`Form Validation Error: ${formName}`, 'warning', {
      tags: { type: 'form_error' },
      extra: { formName, field, error },
    });
  }

  public captureBusinessEvent(event: string, data: Record<string, any>, user?: { id: string; email?: string }): void {
    this.addBreadcrumb({
      message: event,
      category: 'business',
      level: 'info',
      data,
    });

    if (user) {
      this.setUser(user);
    }

    this.captureMessage(`Business Event: ${event}`, 'info', {
      tags: { type: 'business_event' },
      extra: data,
    });
  }

  public capturePerformanceIssue(operation: string, duration: number, threshold: number): void {
    if (duration > threshold) {
      this.captureMessage(`Performance Issue: ${operation}`, 'warning', {
        tags: { type: 'performance' },
        extra: {
          operation,
          duration,
          threshold,
          severity: duration > threshold * 2 ? 'critical' : 'warning',
        },
      });
    }
  }

  // React Error Boundary integration
  public createErrorBoundary() {
    return Sentry.withErrorBoundary;
  }

  public createProfiler() {
    return Sentry.withProfiler;
  }
}

// Export singleton instance
export const sentry = SentryService.getInstance();

// Export Sentry for direct access if needed
export { Sentry };

// React imports (these would be imported from react-router-dom in actual usage)
import React from 'react';

// Mock router functions for now - these would come from react-router-dom
const useLocation = () => ({ pathname: '/' });
const useNavigationType = () => 'POP';
const createRoutesFromChildren = () => [];
const matchRoutes = () => [];
