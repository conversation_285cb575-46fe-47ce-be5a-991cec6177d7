FROM oven/bun:1.0-alpine AS base
WORKDIR /app

# Install dependencies
COPY package.json ./
RUN bun install --no-frozen-lockfile

# Copy shared modules first
# Shared modules not needed for this service

# Copy source
COPY . .

# Build
RUN bun run build

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD bun run -e "fetch('http://localhost:3000/health').then(r => r.ok ? process.exit(0) : process.exit(1))" || exit 1

# Start
CMD ["bun", "run", "start"]
