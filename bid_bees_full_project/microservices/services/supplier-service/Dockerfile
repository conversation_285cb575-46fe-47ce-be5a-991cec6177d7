# Use Bun runtime for optimal performance
FROM oven/bun:1.0-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tzdata

# Copy package files
COPY package.json ./

# Install dependencies
RUN bun install --no-frozen-lockfile

# Copy source code
COPY src ./src
COPY tsconfig.json ./

# Build the application
RUN bun run build

# Create logs directory
RUN mkdir -p logs

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S supplier -u 1001 -G nodejs

# Change ownership of app directory
RUN chown -R supplier:nodejs /app

# Switch to non-root user
USER supplier

# Expose port
EXPOSE 3010

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:3010/health || exit 1

# Start the application
CMD ["bun", "run", "start"]

# Multi-stage build for development
FROM base AS development

# Switch back to root for development dependencies
USER root

# Install development dependencies
RUN bun install --no-frozen-lockfile

# Install additional development tools
RUN apk add --no-cache \
    git \
    openssh-client

# Switch back to supplier user
USER supplier

# Start in development mode
CMD ["bun", "run", "dev"]

# Production stage
FROM base AS production

# Set production environment
ENV NODE_ENV=production

# Remove development files
RUN rm -rf src tsconfig.json

# Start the application
CMD ["bun", "run", "start"]
