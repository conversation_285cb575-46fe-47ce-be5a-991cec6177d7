FROM oven/bun:1.0-alpine

WORKDIR /app

# Copy package files
COPY package.json ./

# Install dependencies
RUN bun install --no-frozen-lockfile

# Copy shared modules first
# Shared modules not needed for this service

# Copy source code
COPY . .

# Build the application
RUN bun run build

# Expose ports
EXPOSE 3000 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start the service
CMD ["bun", "run", "start"]
