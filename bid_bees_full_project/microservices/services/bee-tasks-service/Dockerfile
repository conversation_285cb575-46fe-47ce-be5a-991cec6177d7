# Use Bun runtime for better performance
FROM oven/bun:1.0-alpine AS base

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tzdata

# Copy package files
COPY package.json ./

# Install dependencies
RUN bun install --no-frozen-lockfile

# Copy shared modules first
# Shared modules not needed for this service

# Copy source code
COPY src ./src
COPY tsconfig.json ./

# Build the application
RUN bun run build

# Production stage
FROM oven/bun:1.0-alpine AS production

WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tzdata

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S beeservice -u 1001

# Copy built application
COPY --from=base --chown=beeservice:nodejs /app/dist ./dist
COPY --from=base --chown=beeservice:nodejs /app/node_modules ./node_modules
COPY --from=base --chown=beeservice:nodejs /app/package.json ./

# Create logs directory
RUN mkdir -p logs && chown beeservice:nodejs logs

# Switch to non-root user
USER beeservice

# Expose port
EXPOSE 3003

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3003/health || exit 1

# Start the application
CMD ["bun", "run", "start"]
