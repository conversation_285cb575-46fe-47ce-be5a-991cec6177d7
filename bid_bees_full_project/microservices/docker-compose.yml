version: '3.8'

services:
  # Infrastructure
  mongodb:
    image: mongo:7.0
    container_name: bidbeez-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MON<PERSON><PERSON>_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: bidbeez
    volumes:
      - mongodb_data:/data/db
    networks:
      - bidbeez-network

  redis:
    image: redis:7.2-alpine
    container_name: bidbeez-redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass password
    volumes:
      - redis_data:/data
    networks:
      - bidbeez-network

  # Kafka Infrastructure
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: bidbeez-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - bidbeez-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: bidbeez-kafka
    ports:
      - "9092:9092"
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    networks:
      - bidbeez-network

  # API Gateway (Entry point for microservices)
  api-gateway:
    build: ./services/api-gateway
    container_name: bidbeez-api-gateway
    ports:
      - "8080:3000"  # Different port from existing app
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - bidbeez-network

  # Auth Service
  auth-service:
    build: ./services/auth-service
    container_name: bidbeez-auth-service
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
    depends_on:
      - mongodb
      - redis
    networks:
      - bidbeez-network

  # Tender Service
  tender-service:
    build: ./services/tender-service
    container_name: bidbeez-tender-service
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=tender-service
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network

  # Kafka Service
  kafka-service:
    build: ./services/kafka-service
    container_name: bidbeez-kafka-service
    ports:
      - "3005:3000"
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - WS_PORT=8080
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=kafka-service
      - REDIS_URL=redis://:password@redis:6379
    depends_on:
      - kafka
      - redis
    networks:
      - bidbeez-network

  # Payment Service
  payment-service:
    build: ./services/payment-service
    container_name: bidbeez-payment-service
    ports:
      - "3006:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=payment-service
      - KAFKA_GROUP_ID=payment-service-group
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY:-sk_test_your_stripe_secret_key}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY:-pk_test_your_stripe_publishable_key}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET:-whsec_your_webhook_secret}
      - JWT_SECRET=local-dev-jwt-secret-32-characters-long
      - CURRENCY=ZAR
      - PAYMENT_SUCCESS_URL=http://localhost:5173/payment/success
      - PAYMENT_CANCEL_URL=http://localhost:5173/payment/cancel
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network

  # QueenBee Anchor Service
  queenbee-anchor-service:
    build: ./services/queenbee-anchor-service
    container_name: bidbeez-queenbee-anchor-service
    ports:
      - "8001:8000"
    environment:
      - DEBUG=true
      - SECRET_KEY=dev-secret-key-queenbee-anchor
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=queenbee-anchor-service
      - KAFKA_GROUP_ID=queenbee-anchor-group
      - SUPABASE_DB_HOST=${SUPABASE_DB_HOST}
      - SUPABASE_DB_PASSWORD=${SUPABASE_DB_PASSWORD}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - ANCHOR_BATCH_SIZE=50
      - BLOCKCHAIN_NETWORK=testnet
    depends_on:
      - redis
      - kafka
      - queenbee-celery-worker
    networks:
      - bidbeez-network

  # QueenBee Celery Worker
  queenbee-celery-worker:
    build: ./services/queenbee-anchor-service
    container_name: bidbeez-queenbee-celery-worker
    command: celery -A queenbee_service worker --loglevel=info
    environment:
      - DEBUG=true
      - SECRET_KEY=dev-secret-key-queenbee-anchor
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - SUPABASE_DB_HOST=${SUPABASE_DB_HOST}
      - SUPABASE_DB_PASSWORD=${SUPABASE_DB_PASSWORD}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - ANCHOR_BATCH_SIZE=50
      - BLOCKCHAIN_NETWORK=testnet
    depends_on:
      - redis
      - kafka
    networks:
      - bidbeez-network

  # QueenBee Celery Beat (Scheduler)
  queenbee-celery-beat:
    build: ./services/queenbee-anchor-service
    container_name: bidbeez-queenbee-celery-beat
    command: celery -A queenbee_service beat --loglevel=info
    environment:
      - DEBUG=true
      - SECRET_KEY=dev-secret-key-queenbee-anchor
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - SUPABASE_DB_HOST=${SUPABASE_DB_HOST}
      - SUPABASE_DB_PASSWORD=${SUPABASE_DB_PASSWORD}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    depends_on:
      - redis
      - queenbee-celery-worker
    networks:
      - bidbeez-network

  # ML Service (PyTorch-based)
  ml-service:
    build: ./services/ml-service
    container_name: bidbeez-ml-service
    ports:
      - "3008:3008"
    environment:
      - PORT=3008
      - LOG_LEVEL=INFO
      - MODEL_DIR=/app/models/saved
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=ml-service
      - KAFKA_GROUP_ID=ml-service-group
      - MONGODB_URL=mongodb://mongodb:27017/bidbeez
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - ml-models:/app/models/saved
      - ml-logs:/app/logs
    depends_on:
      - redis
      - kafka
      - mongodb
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3008/admin/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Docling Document Processing Service
  docling-processor:
    build: ./services/docling-processor
    container_name: bidbeez-docling-processor
    ports:
      - "3009:3009"
    environment:
      - PORT=3009
      - LOG_LEVEL=INFO
      - MONGODB_URL=mongodb://mongodb:27017/bidbeez
      - REDIS_URL=redis://:password@redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - docling-documents:/app/documents
      - docling-logs:/app/logs
    depends_on:
      - mongodb
      - redis
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3009/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Supplier Service
  supplier-service:
    build: ./services/supplier-service
    container_name: bidbeez-supplier-service
    ports:
      - "3010:3010"
    environment:
      - NODE_ENV=development
      - PORT=3010
      - LOG_LEVEL=INFO
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=supplier-service
      - KAFKA_GROUP_ID=supplier-service-group
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGIN=http://localhost:3000,http://localhost:8080
      - QUEENBEE_ANCHOR_URL=http://queenbee-anchor-service:8001
      - BLOCKCHAIN_ENABLED=true
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - supplier-logs:/app/logs
      - supplier-uploads:/app/uploads
    depends_on:
      - redis
      - kafka
      - mongodb
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3010/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # BEE Tasks Service
  bee-tasks-service:
    build: ./services/bee-tasks-service
    container_name: bidbeez-bee-tasks-service
    ports:
      - "3011:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - LOG_LEVEL=INFO
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=bee-tasks-service
      - KAFKA_GROUP_ID=bee-tasks-group
      - JWT_SECRET=${JWT_SECRET}
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080
      - COMMISSION_RATE=0.15
      - URGENT_TASK_MULTIPLIER=1.5
      - DEFAULT_TASK_RADIUS=50
      - MAX_TASK_RADIUS=200
      - MAX_TASKS_PER_BEE=5
      - TASK_TIMEOUT_HOURS=24
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - bee-tasks-logs:/app/logs
    depends_on:
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # User Service
  user-service:
    build: ./services/user-service
    container_name: bidbeez-user-service
    ports:
      - "3012:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=user-service
      - KAFKA_GROUP_ID=user-service-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Bidding Service
  bidding-service:
    build: ./services/bidding-service
    container_name: bidbeez-bidding-service
    ports:
      - "3013:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=bidding-service
      - KAFKA_GROUP_ID=bidding-service-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Notification Service
  notification-service:
    build: ./services/notification-service
    container_name: bidbeez-notification-service
    ports:
      - "3014:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=notification-service
      - KAFKA_GROUP_ID=notification-service-group
      - JWT_SECRET=${JWT_SECRET}
      - EMAIL_SERVICE=${EMAIL_SERVICE}
      - EMAIL_API_KEY=${EMAIL_API_KEY}
      - SMS_SERVICE=${SMS_SERVICE}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - FCM_SERVER_KEY=${FCM_SERVER_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Document Service
  document-service:
    build: ./services/document-service
    container_name: bidbeez-document-service
    ports:
      - "3015:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=document-service
      - KAFKA_GROUP_ID=document-service-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_S3_BUCKET=${AWS_S3_BUCKET}
      - SENTRY_DSN=${SENTRY_DSN}
    volumes:
      - document-uploads:/app/uploads
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Map Service
  map-service:
    build: ./services/map-service
    container_name: bidbeez-map-service
    ports:
      - "3016:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=map-service
      - KAFKA_GROUP_ID=map-service-group
      - JWT_SECRET=${JWT_SECRET}
      - MAPBOX_ACCESS_TOKEN=${MAPBOX_ACCESS_TOKEN}
      - GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Analytics Service
  analytics-service:
    build: ./services/analytics-service
    container_name: bidbeez-analytics-service
    ports:
      - "3017:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=analytics-service
      - KAFKA_GROUP_ID=analytics-service-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - GOOGLE_ANALYTICS_ID=${GOOGLE_ANALYTICS_ID}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Courier Service
  courier-service:
    build: ./services/courier-service
    container_name: bidbeez-courier-service
    ports:
      - "3018:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=courier-service
      - KAFKA_GROUP_ID=courier-service-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - MAPBOX_ACCESS_TOKEN=${MAPBOX_ACCESS_TOKEN}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Transport Service
  transport-service:
    build: ./services/transport-service
    container_name: bidbeez-transport-service
    ports:
      - "3019:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=transport-service
      - KAFKA_GROUP_ID=transport-service-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Student Verification Service
  student-verification-service:
    build: ./services/student-verification-service
    container_name: bidbeez-student-verification-service
    ports:
      - "3020:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=student-verification-service
      - KAFKA_GROUP_ID=student-verification-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ContractorSync Service
  contractorsync-service:
    build: ./services/contractorsync-service
    container_name: bidbeez-contractorsync-service
    ports:
      - "3021:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=contractorsync-service
      - KAFKA_GROUP_ID=contractorsync-service-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # SkillSync Service
  skillsync-service:
    build: ./services/skillsync-service
    container_name: bidbeez-skillsync-service
    ports:
      - "3022:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=skillsync-service
      - KAFKA_GROUP_ID=skillsync-service-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ToolSync Service
  toolsync-service:
    build: ./services/toolsync-service
    container_name: bidbeez-toolsync-service
    ports:
      - "3023:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - MONGODB_URI=***************************************************************
      - REDIS_URL=redis://:password@redis:6379
      - KAFKA_BROKERS=kafka:29092
      - KAFKA_CLIENT_ID=toolsync-service
      - KAFKA_GROUP_ID=toolsync-service-group
      - JWT_SECRET=${JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SENTRY_DSN=${SENTRY_DSN}
    depends_on:
      - mongodb
      - redis
      - kafka
    networks:
      - bidbeez-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mongodb_data:
  redis_data:
  ml-models:
  ml-logs:
  docling-documents:
  docling-logs:
  supplier-logs:
  supplier-uploads:
  bee-tasks-logs:
  document-uploads:

networks:
  bidbeez-network:
    driver: bridge
