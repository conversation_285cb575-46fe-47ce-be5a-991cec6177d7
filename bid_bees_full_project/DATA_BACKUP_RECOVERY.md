# Data Backup and Recovery Plan

This document outlines the strategy for backing up and recovering data for the BidBees platform. A robust backup plan is critical for business continuity and disaster recovery.

## 1. Data Stores

The BidBees platform utilizes three primary data stores:
- **PostgreSQL (via AWS RDS/Supabase):** The main transactional database for core application data.
- **MongoDB (via AWS DocumentDB/MongoDB Atlas):** Used for services requiring flexible schema data.
- **Redis (via AWS ElastiCache):** Used for caching, session storage, and real-time messaging.

---

## 2. Backup Strategy

### 2.1. PostgreSQL (AWS RDS)

- **Automated Snapshots:**
  - **Frequency:** Daily automated snapshots are enabled.
  - **Retention:** Snapshots are retained for **14 days**.
  - **Backup Window:** A daily 30-minute backup window is scheduled during off-peak hours (e.g., 02:00-02:30 UTC).

- **Point-in-Time Recovery (PITR):**
  - **Enabled:** PITR is enabled, allowing for restoration to any specific second within the retention period.
  - **Granularity:** 5-minute granularity.

- **Manual Snapshots:**
  - Manual snapshots will be taken before any major schema migration or significant application upgrade. These snapshots will be retained for **90 days**.

### 2.2. MongoDB (AWS DocumentDB / Atlas)

- **Automated Backups:**
  - **Frequency:** Daily automated backups are enabled.
  - **Retention:** Backups are retained for **14 days**.
  - **Backup Window:** Scheduled during off-peak hours (e.g., 02:30-03:00 UTC).

- **Point-in-Time Recovery (PITR):**
  - Enabled with a 24-hour recovery window.

### 2.3. Redis (AWS ElastiCache)

- **Automated Snapshots:**
  - **Frequency:** Daily automated snapshots are enabled.
  - **Retention:** Snapshots are retained for **7 days**.
  - **Rationale:** Redis data is primarily for caching and is less critical than primary data stores. A shorter retention period is acceptable.

---

## 3. Recovery Strategy

### 3.1. Recovery Time Objective (RTO) & Recovery Point Objective (RPO)

- **RTO:** 2 hours. The entire system should be restorable within 2 hours of a declared disaster.
- **RPO:** 5 minutes. In the event of a failure, a maximum of 5 minutes of data loss is acceptable.

### 3.2. Recovery Procedures

A detailed, step-by-step recovery procedure will be maintained in the internal team wiki and linked here. The general steps are:

1.  **Declare a Disaster:** The on-call engineering lead declares a disaster and initiates the recovery process.
2.  **Restore Databases:**
    -   Restore the PostgreSQL database from the latest PITR backup or the most recent snapshot.
    -   Restore the MongoDB database from its latest backup.
    -   Restore the Redis cluster from its latest snapshot.
3.  **Update Application Configuration:** Update the environment variables and application configurations of all microservices to point to the newly restored database instances.
4.  **Restart Services:** Perform a rolling restart of all application services.
5.  **Verify System Health:** Run automated health checks and integration tests to verify that the system is fully functional.
6.  **Post-Mortem:** Conduct a post-mortem analysis to determine the root cause of the failure and improve the recovery process.

### 3.3. Testing the Recovery Plan

- **Frequency:** The disaster recovery plan will be tested **quarterly**.
- **Environment:** Tests will be conducted in a dedicated staging environment that mirrors the production infrastructure.
- **Process:** The test will involve a full restoration of the production backups to the staging environment to validate the integrity of the backups and the accuracy of the recovery procedure.

---

## 4. Data Retention and Archiving

- **Active Data:** Data within the backup retention periods (7-14 days) is considered active.
- **Long-Term Archiving:** For compliance and auditing purposes, a monthly snapshot of the PostgreSQL database will be exported to Amazon S3 Glacier Deep Archive and retained for **7 years**.
