# Code Quality and Testing Strategy

This document outlines the standards and practices for maintaining high code quality, ensuring consistency, and verifying application correctness through testing.

## 1. Code Style and Formatting

- **Formatter:** We use **Prettier** for automated code formatting. This ensures a consistent style across the entire codebase.
- **Linter:** We use **ESLint** to identify and fix problems in our JavaScript/TypeScript code. The configuration is defined in `.eslintrc.js`.

### Scripts

- **Check Formatting:** `npm run format:check`
- **Apply Formatting:** `npm run format`
- **Run Linter:** `npm run lint`
- **Fix Lint Errors:** `npm run lint:fix`

## 2. Testing

We employ a multi-layered testing strategy to ensure the application is reliable and robust.

### 2.1. Testing Framework

- **Unit & Integration Tests:** We use **Vitest** as our primary testing framework for its speed and modern features.
- **End-to-End (E2E) Tests:** We use **Playwright** for E2E tests to simulate real user interactions in a browser.

### 2.2. Test Coverage

- **Goal:** We aim for a minimum of **80% test coverage** across the codebase.
- **Enforcement:** This is automatically enforced in our CI/CD pipeline. Builds will fail if the coverage drops below this threshold.
- **Reporting:** Coverage reports are generated with every test run.

### 2.3. Running Tests

- **Run All Tests:** `npm run test`
- **Run Tests with Coverage:** `npm run test:coverage`
- **Run E2E Tests:** `npm run test:e2e`

## 3. CI/CD Pipeline

Our CI/CD pipeline on GitHub Actions automates our quality checks. Every pull request automatically triggers the following jobs:

1.  **Lint & Format Check:** Ensures the code adheres to our style guide.
2.  **Build:** Verifies that the application builds successfully.
3.  **Test & Coverage:** Runs all unit and integration tests and checks if the coverage meets our 80% threshold.

Pull requests cannot be merged unless all these checks pass.
