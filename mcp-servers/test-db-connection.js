import WebSocket from 'ws';

setTimeout(() => {
  const ws = new WebSocket('ws://localhost:8766');

  ws.on('open', () => {
    console.log('Connected to Database MCP Server');
    const message = {
      type: 'table_list',
      requestId: '12345'
    };
    ws.send(JSON.stringify(message));
  });

  ws.on('message', (data) => {
    console.log('Received message:', JSON.parse(data));
    ws.close();
  });

  ws.on('close', () => {
    console.log('Disconnected from Database MCP Server');
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });
}, 5000); // Wait 5 seconds before connecting